import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { verifyJwt } from "@/lib/jwt";
import { HeroUIClientProvider } from "@/components/providers/heroui-provider";
import { DashboardContent } from "@/components/dashboard/dashboard-content";

export default async function DashboardPage() {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    redirect("/login");
  }

  const decoded = verifyJwt(token) as { id: string; email: string };

  return (
    <HeroUIClientProvider>
      <DashboardContent userEmail={decoded.email} />
    </HeroUIClientProvider>
  );
}
