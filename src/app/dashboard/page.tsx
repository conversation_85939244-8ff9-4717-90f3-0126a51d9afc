// app/dashboard/page.tsx

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { verifyJwt } from "@/lib/jwt";

export default async function DashboardPage() {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    redirect("/login");
  }

  const decoded = verifyJwt(token) as { id: string; email: string };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <h1 className="text-3xl font-bold">Welcome, {decoded.email}</h1>
      <form action="/api/auth/signout" method="POST">
        <button
          type="submit"
          className="px-6 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition"
        >
          Sign Out
        </button>
      </form>
    </div>
  );
}
