"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CardHeader,
  In<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@heroui/react";
import { HeroUIClientProvider } from "@/components/providers/heroui-provider";

function LoginForm() {
  const [email, setEmail] = useState("");
  const [code, setCode] = useState("");
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "info">(
    "info",
  );
  const [isLoadingSendOtp, setIsLoadingSendOtp] = useState(false);
  const [isLoadingVerifyOtp, setIsLoadingVerifyOtp] = useState(false);
  const [otpSent, setOtpSent] = useState(false);

  async function handleSendOtp(e: React.FormEvent) {
    e.preventDefault();
    setIsLoadingSendOtp(true);
    setMessage("Sending OTP...");
    setMessageType("info");

    try {
      const res = await fetch("/api/auth/request-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      if (res.ok) {
        setMessage("OTP sent! Check your email.");
        setMessageType("success");
        setOtpSent(true);
      } else {
        setMessage("Failed to send OTP.");
        setMessageType("error");
      }
    } catch {
      setMessage("Network error.");
      setMessageType("error");
    } finally {
      setIsLoadingSendOtp(false);
    }
  }

  async function handleVerifyOtp(e: React.FormEvent) {
    e.preventDefault();
    setIsLoadingVerifyOtp(true);
    setMessage("Verifying OTP...");
    setMessageType("info");

    try {
      const res = await fetch("/api/auth/verify-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, code }),
      });
      const data = await res.json();
      if (res.ok) {
        setMessage("Logged in successfully!");
        setMessageType("success");
        // Redirect or update UI accordingly
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1000);
      } else {
        setMessage(data.error || "Invalid OTP");
        setMessageType("error");
      }
    } catch {
      setMessage("Network error.");
      setMessageType("error");
    } finally {
      setIsLoadingVerifyOtp(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 to-purple-50 p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="flex flex-col gap-3 pb-6">
          <div className="flex flex-col items-center">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              💕 Lovebook
            </h1>
            <p className="text-small text-default-500 mt-2">
              Sign in to create your love story
            </p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="gap-6">
          {/* Send OTP Form */}
          <form onSubmit={handleSendOtp} className="flex flex-col gap-4">
            <Input
              type="email"
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              isRequired
              variant="bordered"
              color="secondary"
              classNames={{
                input: "text-small",
                inputWrapper:
                  "border-1 border-default-200 hover:border-secondary-300",
              }}
            />
            <Button
              type="submit"
              color="secondary"
              variant="solid"
              size="lg"
              isLoading={isLoadingSendOtp}
              className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold"
            >
              {isLoadingSendOtp ? "Sending..." : "Send OTP"}
            </Button>
          </form>

          {/* Verify OTP Form */}
          {otpSent && (
            <>
              <Divider />
              <form onSubmit={handleVerifyOtp} className="flex flex-col gap-4">
                <Input
                  type="text"
                  label="OTP Code"
                  placeholder="Enter 6-digit code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  isRequired
                  variant="bordered"
                  color="success"
                  maxLength={6}
                  classNames={{
                    input: "text-small text-center tracking-widest",
                    inputWrapper:
                      "border-1 border-default-200 hover:border-success-300",
                  }}
                />
                <Button
                  type="submit"
                  color="success"
                  variant="solid"
                  size="lg"
                  isLoading={isLoadingVerifyOtp}
                  className="font-semibold"
                >
                  {isLoadingVerifyOtp ? "Verifying..." : "Verify & Login"}
                </Button>
              </form>
            </>
          )}

          {/* Message Display */}
          {message && (
            <Chip
              color={
                messageType === "success"
                  ? "success"
                  : messageType === "error"
                    ? "danger"
                    : "primary"
              }
              variant="flat"
              className="w-full justify-center py-2"
            >
              {message}
            </Chip>
          )}
        </CardBody>
      </Card>
    </div>
  );
}

export default function LoginPage() {
  return (
    <HeroUIClientProvider>
      <LoginForm />
    </HeroUIClientProvider>
  );
}
